"use client";

import { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { FileText, User, Database, Image } from "lucide-react";
import {
  EXAMPLE_METADATA,
  createDocumentSummary,
  getDocumentField,
  getAvailableFields,
} from "@/lib/pdf-metadata-utils";

export function MetadataDemo() {
  const [showRawJson, setShowRawJson] = useState(false);
  const metadata = EXAMPLE_METADATA;
  const summary = createDocumentSummary(metadata);
  const fields = getAvailableFields(metadata);

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            PDF Metadata Structure Demo
          </CardTitle>
          <CardDescription>
            This demonstrates the JSON metadata that gets embedded in generated
            PDF documents
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Document Summary */}
          <div>
            <h3 className="text-lg font-semibold mb-3">Document Summary</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <FileText className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">Document Name:</span>
                </div>
                <p className="text-sm text-muted-foreground ml-6">
                  {summary.documentName}
                </p>
              </div>

              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">Applicant:</span>
                </div>
                <p className="text-sm text-muted-foreground ml-6">
                  {summary.applicantName}
                </p>
              </div>

              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Database className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">System:</span>
                </div>
                <p className="text-sm text-muted-foreground ml-6">
                  {summary.system}
                </p>
              </div>

              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <FileText className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">Fields:</span>
                </div>
                <p className="text-sm text-muted-foreground ml-6">
                  {summary.fieldCount} fields
                </p>
              </div>
            </div>

            <div className="flex items-center gap-2 mt-4">
              <Badge variant="secondary">{summary.fieldCount} fields</Badge>
              {summary.hasPhoto && (
                <Badge variant="outline" className="flex items-center gap-1">
                  <Image className="h-3 w-3" />
                  Photo included
                </Badge>
              )}
              <Badge variant="outline">{summary.system}</Badge>
            </div>
          </div>

          <Separator />

          {/* Document Data Fields */}
          <div>
            <h3 className="text-lg font-semibold mb-3">Document Data Fields</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {fields.map((field) => {
                const value = getDocumentField(metadata, field);
                const isPhoto = field === "applicants_photo";

                return (
                  <div
                    key={field}
                    className="flex justify-between items-center p-2 bg-muted/50 rounded"
                  >
                    <span className="text-sm font-medium">
                      {field.replace(/_/g, " ")}
                    </span>
                    <span className="text-sm text-muted-foreground">
                      {isPhoto ? "[Base64 Image Data]" : value}
                    </span>
                  </div>
                );
              })}
            </div>
          </div>

          <Separator />

          {/* Raw JSON Toggle */}
          <div>
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-lg font-semibold">Raw JSON Metadata</h3>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowRawJson(!showRawJson)}
              >
                {showRawJson ? "Hide" : "Show"} JSON
              </Button>
            </div>

            {showRawJson && (
              <div className="bg-muted p-4 rounded-lg overflow-auto">
                <pre className="text-xs">
                  {JSON.stringify(metadata, null, 2)}
                </pre>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Usage Information */}
      <Card>
        <CardHeader>
          <CardTitle>How It Works</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <h4 className="font-medium">1. Document Generation</h4>
            <p className="text-sm text-muted-foreground">
              When a user generates a document, the system creates a JSON
              metadata object containing:
            </p>
            <ul className="text-sm text-muted-foreground ml-4 space-y-1">
              <li>• Document name with applicant information</li>
              <li>• Full applicant name (formatted)</li>
              <li>• All form field data including photos (as base64)</li>
              <li>• Template information and generation details</li>
            </ul>
          </div>

          <div className="space-y-2">
            <h4 className="font-medium">2. PDF Embedding</h4>
            <p className="text-sm text-muted-foreground">
              The JSON metadata is embedded in the PDF in two ways:
            </p>
            <ul className="text-sm text-muted-foreground ml-4 space-y-1">
              <li>• As internal metadata in the PDF structure</li>
              <li>• As invisible white text at the bottom of the document</li>
            </ul>
          </div>

          <div className="space-y-2">
            <h4 className="font-medium">3. Extraction</h4>
            <p className="text-sm text-muted-foreground">
              The metadata can be extracted using PDF text extraction tools or
              by accessing the PDF&apos;s internal metadata structure.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
